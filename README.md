# Instagram Follower Tracker

A Node.js command-line tool for tracking Instagram followers and detecting changes over time.

## Features

- 📊 Track follower changes (new followers, unfollowers, status changes)
- 💾 SQLite database for persistent storage
- 📈 CSV report generation
- 🔍 Historical data analysis
- ⚙️ Configurable settings
- 📝 Comprehensive logging

## Installation

1. Clone or download this repository
2. Install dependencies:
   ```bash
   npm install
   ```

## Usage

### Basic Tracking

Track followers from JSON files:

```bash
npm start track -f followers.json -s friendship_status.json
```

### Command Options

#### Track Command
```bash
npm start track [options]
```

**Options:**
- `-f, --followers <path>` - Path to followers JSON file (required)
- `-s, --status <path>` - Path to friendship status JSON file (optional)
- `-o, --output <path>` - Output directory for CSV reports (default: ./reports)
- `-d, --database <path>` - Database file path (default: ./data/followers.db)
- `--no-csv` - Skip CSV report generation

**Examples:**
```bash
# Basic tracking with followers only
npm start track -f my_followers.json

# Track with both followers and friendship status
npm start track -f followers.json -s status.json

# Custom output directory
npm start track -f followers.json -o ./my_reports

# Custom database location
npm start track -f followers.json -d ./custom/db.sqlite
```

#### Report Command
```bash
npm start report [options]
```

**Options:**
- `-o, --output <path>` - Output directory for reports (default: ./reports)
- `-d, --database <path>` - Database file path (default: ./data/followers.db)
- `--since <date>` - Show changes since date (YYYY-MM-DD)
- `--type <type>` - Filter by change type (NEW_FOLLOWER, UNFOLLOWED, STATUS_CHANGE)
- `--limit <number>` - Limit number of results

**Examples:**
```bash
# Generate report of all changes
npm start report

# Show only new followers since a specific date
npm start report --type NEW_FOLLOWER --since 2024-01-01

# Show last 50 changes
npm start report --limit 50
```

## Input File Formats

### Followers JSON Format
```json
{
  "users": [
    {
      "pk": "3680985246",
      "username": "dalipajaldo",
      "full_name": "Aldo Dalipaj",
      "is_private": true,
      "is_verified": false,
      "profile_pic_url": "https://...",
      ...
    }
  ],
  "status": "ok"
}
```

### Friendship Status JSON Format
```json
{
  "friendship_statuses": {
    "1622272917": {
      "following": false,
      "incoming_request": false,
      "is_bestie": false,
      "is_private": false,
      "is_restricted": false,
      "outgoing_request": false,
      "is_feed_favorite": false
    }
  },
  "status": "ok"
}
```

## Output

### Console Output
The tool provides real-time feedback including:
- Number of followers processed
- Changes detected (new followers, unfollowers, status changes)
- Summary statistics
- File locations for generated reports

### CSV Reports
Generated CSV files include:
- `follower_changes_[timestamp].csv` - All detected changes
- `current_followers_[timestamp].csv` - Current follower list
- `friendship_statuses_[timestamp].csv` - Current friendship statuses

### Database Storage
All data is stored in SQLite database with tables:
- `users` - Follower information
- `friendship_statuses` - Relationship statuses
- `follower_changes` - Historical change records

## Configuration

Create a `follower-tracker.config.json` file in your project root to customize settings:

```json
{
  "database": {
    "path": "./data/followers.db",
    "backupOnUpdate": true
  },
  "output": {
    "directory": "./reports",
    "generateCSV": true,
    "timestampFiles": true
  },
  "logging": {
    "level": "info",
    "file": "./logs/follower-tracker.log",
    "enableConsole": true
  }
}
```

## Project Structure

```
├── src/
│   ├── models/          # Data models (User, FriendshipStatus, FollowerChange)
│   ├── services/        # Business logic (Database, Comparison)
│   ├── utils/           # Utilities (FileIO, CSV, Logging, Config)
│   └── index.js         # Main entry point
├── config/              # Default configuration
├── data/                # Database storage
├── reports/             # Generated CSV reports
├── logs/                # Application logs
└── package.json
```

## Error Handling

The tool includes comprehensive error handling for:
- Missing or invalid JSON files
- Database connection issues
- File permission problems
- Invalid data formats

All errors are logged to both console and log files for debugging.

## Troubleshooting

### Common Issues

1. **File not found errors**
   - Ensure the JSON file paths are correct
   - Check file permissions

2. **Database errors**
   - Ensure the data directory is writable
   - Check disk space

3. **Invalid JSON format**
   - Validate your JSON files using a JSON validator
   - Ensure the files match the expected format

### Logs

Check the log file at `./logs/follower-tracker.log` for detailed error information.

## License

ISC License
