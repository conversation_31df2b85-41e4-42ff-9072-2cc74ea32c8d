# Usage Examples

This document provides practical examples of how to use the Instagram Follower Tracker.

## Quick Start

### 1. Run Basic Test
```bash
npm test
```
This will run basic functionality tests to ensure everything is working.

### 2. Test with Sample Data
```bash
npm run test:sample
```
This will run the tracker with the provided sample data files.

## Real Usage Scenarios

### Scenario 1: First Time Setup

When running for the first time with your Instagram data:

```bash
# Track followers only
npm start track -f my_instagram_followers.json

# Track followers with friendship status
npm start track -f my_instagram_followers.json -s my_friendship_status.json
```

**Expected Output:**
```
🔍 Starting follower tracking...

📖 Reading current followers data...
Found 150 current followers
📖 Reading friendship status data...
Found 150 friendship statuses
🗄️  Loading previous data from database...
Found 0 previous followers in database
🔄 Comparing followers and detecting changes...

=== FOLLOWER TRACKING SUMMARY ===
Timestamp: 2024-01-15T10:30:00.000Z

Total Current Followers: 150
Total Previous Followers: 0
Net Change: +150

NEW FOLLOWERS (150):
  + da<PERSON><PERSON>ldo (<PERSON><PERSON>) - Private
  + antwnhsvenetsanos (Antwnhs Venetsanos) - Public
  ...

💾 Saving current data to database...
Saved 150 changes to database

📊 Generating CSV reports...
Successfully wrote 150 follower changes to ./reports/follower_changes_2024-01-15T10-30-00-000Z.csv
Successfully wrote 150 current followers to ./reports/current_followers_2024-01-15T10-30-00-000Z.csv
Reports saved to: ./reports

✅ Tracking completed successfully!
```

### Scenario 2: Regular Tracking (Detecting Changes)

After running the tracker multiple times, you'll see actual changes:

```bash
npm start track -f updated_followers.json -s updated_status.json
```

**Expected Output:**
```
=== FOLLOWER TRACKING SUMMARY ===
Timestamp: 2024-01-16T10:30:00.000Z

Total Current Followers: 152
Total Previous Followers: 150
Net Change: +2

NEW FOLLOWERS (3):
  + newuser123 (John Doe) - Public
  + privatefriend (Jane Smith) - Private
  + verified_account (Celebrity Name) - Public

UNFOLLOWED (1):
  - olduser456 (Former Friend) - Private

No status changes detected.
```

### Scenario 3: Generating Historical Reports

View changes from the last week:
```bash
npm start report --since 2024-01-08
```

View only new followers:
```bash
npm start report --type NEW_FOLLOWER --limit 20
```

View all unfollowers:
```bash
npm start report --type UNFOLLOWED
```

## Advanced Usage

### Custom Configuration

Create a `follower-tracker.config.json` file:

```json
{
  "database": {
    "path": "./my_data/instagram.db"
  },
  "output": {
    "directory": "./my_reports",
    "generateCSV": true
  },
  "logging": {
    "level": "debug",
    "file": "./logs/detailed.log"
  }
}
```

Then run:
```bash
npm start track -f followers.json
```

### Batch Processing

Process multiple files in sequence:

```bash
# Monday data
npm start track -f monday_followers.json -s monday_status.json

# Tuesday data  
npm start track -f tuesday_followers.json -s tuesday_status.json

# Generate weekly report
npm start report --since 2024-01-08 --limit 100
```

### Custom Output Locations

```bash
# Custom database and output directory
npm start track -f followers.json -d ./custom/db.sqlite -o ./custom_reports

# Skip CSV generation
npm start track -f followers.json --no-csv
```

## Understanding the Output

### CSV Files Generated

1. **follower_changes_[timestamp].csv**
   - Contains all detected changes
   - Columns: ID, User PK, Username, Full Name, Change Type, Timestamp, Previous Status, Current Status, Metadata

2. **current_followers_[timestamp].csv**
   - Snapshot of current followers
   - Columns: User PK, Username, Full Name, Is Private, Is Verified, Profile Picture URL

3. **friendship_statuses_[timestamp].csv**
   - Current friendship status data
   - Columns: User PK, Following, Incoming Request, Is Bestie, Is Private, Is Restricted, Outgoing Request, Is Feed Favorite

### Database Structure

The SQLite database contains three main tables:

- `users` - Stores follower information with timestamps
- `friendship_statuses` - Stores relationship status data
- `follower_changes` - Historical log of all changes

### Change Types

- **NEW_FOLLOWER** - Someone started following you
- **UNFOLLOWED** - Someone stopped following you  
- **STATUS_CHANGE** - Friendship status changed (e.g., became private, started following back)

## Troubleshooting Examples

### Problem: File Not Found
```bash
npm start track -f nonexistent.json
```
**Error:** `❌ File not found: nonexistent.json`
**Solution:** Check the file path and ensure the file exists

### Problem: Invalid JSON
```bash
npm start track -f broken.json
```
**Error:** `❌ Invalid JSON format in file`
**Solution:** Validate your JSON file using a JSON validator

### Problem: Permission Denied
**Error:** `❌ Permission denied: ./data/followers.db`
**Solution:** Ensure the data directory is writable or run with appropriate permissions

## Integration Examples

### Automated Daily Tracking

Create a shell script `daily_track.sh`:

```bash
#!/bin/bash
DATE=$(date +%Y-%m-%d)
echo "Running Instagram follower tracking for $DATE"

# Run the tracker
npm start track -f "data/followers_$DATE.json" -s "data/status_$DATE.json"

# Generate daily report
npm start report --since $DATE > "reports/daily_report_$DATE.txt"

echo "Tracking completed for $DATE"
```

### Data Analysis

After collecting data for several days, you can analyze trends:

```bash
# See all changes from the last month
npm start report --since 2024-01-01

# Focus on unfollowers to understand churn
npm start report --type UNFOLLOWED --since 2024-01-01

# Track growth by looking at new followers
npm start report --type NEW_FOLLOWER --since 2024-01-01
```

This tool helps you understand your Instagram follower dynamics and track changes over time!
