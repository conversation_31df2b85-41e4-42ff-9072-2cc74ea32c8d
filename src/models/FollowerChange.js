/**
 * FollowerChange model representing a change in follower status
 */
class FollowerChange {
    constructor(data) {
        this.id = data.id || null; // Auto-generated by database
        this.pk = data.pk; // User's pk
        this.username = data.username;
        this.full_name = data.full_name;
        this.change_type = data.change_type; // 'NEW_FOLLOWER', 'UNFOLLOWED', 'STATUS_CHANGE'
        this.timestamp = data.timestamp || new Date().toISOString();
        this.previous_status = data.previous_status || null;
        this.current_status = data.current_status || null;
        this.metadata = data.metadata || {}; // Additional data like profile changes
    }

    /**
     * Create a new follower change record
     */
    static newFollower(user, friendshipStatus = null) {
        return new FollowerChange({
            pk: user.pk,
            username: user.username,
            full_name: user.full_name,
            change_type: 'NEW_FOLLOWER',
            current_status: friendshipStatus ? JSON.stringify(friendshipStatus.toCSVRow()) : null,
            metadata: {
                is_private: user.is_private,
                is_verified: user.is_verified,
                profile_pic_url: user.profile_pic_url
            }
        });
    }

    /**
     * Create an unfollowed change record
     */
    static unfollowed(user, previousFriendshipStatus = null) {
        return new FollowerChange({
            pk: user.pk,
            username: user.username,
            full_name: user.full_name,
            change_type: 'UNFOLLOWED',
            previous_status: previousFriendshipStatus ? JSON.stringify(previousFriendshipStatus.toCSVRow()) : null,
            metadata: {
                is_private: user.is_private,
                is_verified: user.is_verified,
                profile_pic_url: user.profile_pic_url
            }
        });
    }

    /**
     * Create a status change record
     */
    static statusChange(user, previousStatus, currentStatus) {
        return new FollowerChange({
            pk: user.pk,
            username: user.username,
            full_name: user.full_name,
            change_type: 'STATUS_CHANGE',
            previous_status: previousStatus ? JSON.stringify(previousStatus.toCSVRow()) : null,
            current_status: currentStatus ? JSON.stringify(currentStatus.toCSVRow()) : null,
            metadata: {
                is_private: user.is_private,
                is_verified: user.is_verified,
                profile_pic_url: user.profile_pic_url
            }
        });
    }

    /**
     * Get a representation for CSV export
     */
    toCSVRow() {
        return {
            id: this.id,
            pk: this.pk,
            username: this.username,
            full_name: this.full_name,
            change_type: this.change_type,
            timestamp: this.timestamp,
            previous_status: this.previous_status,
            current_status: this.current_status,
            metadata: JSON.stringify(this.metadata)
        };
    }

    /**
     * Create FollowerChange instance from database row
     */
    static fromDBRow(row) {
        return new FollowerChange({
            id: row.id,
            pk: row.pk,
            username: row.username,
            full_name: row.full_name,
            change_type: row.change_type,
            timestamp: row.timestamp,
            previous_status: row.previous_status,
            current_status: row.current_status,
            metadata: row.metadata ? JSON.parse(row.metadata) : {}
        });
    }

    /**
     * Convert to database row format
     */
    toDBRow() {
        return {
            pk: this.pk,
            username: this.username,
            full_name: this.full_name,
            change_type: this.change_type,
            timestamp: this.timestamp,
            previous_status: this.previous_status,
            current_status: this.current_status,
            metadata: JSON.stringify(this.metadata)
        };
    }
}

module.exports = FollowerChange;
