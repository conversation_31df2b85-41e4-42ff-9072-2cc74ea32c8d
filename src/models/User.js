/**
 * User model representing an Instagram user from the followers JSON
 */
class User {
    constructor(userData) {
        this.pk = userData.pk;
        this.pk_id = userData.pk_id;
        this.id = userData.id;
        this.full_name = userData.full_name;
        this.username = userData.username;
        this.is_private = userData.is_private;
        this.is_verified = userData.is_verified;
        this.profile_pic_url = userData.profile_pic_url;
        this.profile_pic_id = userData.profile_pic_id;
        this.fbid_v2 = userData.fbid_v2;
        this.strong_id__ = userData.strong_id__;
        this.third_party_downloads_enabled = userData.third_party_downloads_enabled;
        this.has_anonymous_profile_picture = userData.has_anonymous_profile_picture;
        this.account_badges = userData.account_badges || [];
        this.latest_reel_media = userData.latest_reel_media;
        this.is_favorite = userData.is_favorite;
    }

    /**
     * Get the unique identifier for this user (using pk as primary key)
     */
    getId() {
        return this.pk;
    }

    /**
     * Get a simplified representation for CSV export
     */
    toCSVRow() {
        return {
            username: this.username,
            full_name: this.full_name,
            is_private: this.is_private ? 'Yes' : 'No',
            is_verified: this.is_verified ? 'Yes' : 'No',
            pk: this.pk
        };
    }

    /**
     * Create User instance from database row
     */
    static fromDBRow(row) {
        return new User({
            pk: row.pk,
            pk_id: row.pk_id,
            id: row.id,
            full_name: row.full_name,
            username: row.username,
            is_private: Boolean(row.is_private),
            is_verified: Boolean(row.is_verified),
            profile_pic_url: row.profile_pic_url,
            profile_pic_id: row.profile_pic_id,
            fbid_v2: row.fbid_v2,
            strong_id__: row.strong_id__,
            third_party_downloads_enabled: row.third_party_downloads_enabled,
            has_anonymous_profile_picture: Boolean(row.has_anonymous_profile_picture),
            account_badges: row.account_badges ? JSON.parse(row.account_badges) : [],
            latest_reel_media: row.latest_reel_media,
            is_favorite: Boolean(row.is_favorite)
        });
    }

    /**
     * Convert to database row format
     */
    toDBRow() {
        return {
            pk: this.pk,
            pk_id: this.pk_id,
            id: this.id,
            full_name: this.full_name,
            username: this.username,
            is_private: this.is_private ? 1 : 0,
            is_verified: this.is_verified ? 1 : 0,
            profile_pic_url: this.profile_pic_url,
            profile_pic_id: this.profile_pic_id,
            fbid_v2: this.fbid_v2,
            strong_id__: this.strong_id__,
            third_party_downloads_enabled: this.third_party_downloads_enabled,
            has_anonymous_profile_picture: this.has_anonymous_profile_picture ? 1 : 0,
            account_badges: JSON.stringify(this.account_badges),
            latest_reel_media: this.latest_reel_media,
            is_favorite: this.is_favorite ? 1 : 0
        };
    }
}

module.exports = User;
