const fs = require('fs').promises;
const path = require('path');

/**
 * Simple logging utility
 */
class Logger {
    constructor(options = {}) {
        this.logLevel = options.logLevel || 'info';
        this.logFile = options.logFile || null;
        this.enableConsole = options.enableConsole !== false;
        
        this.levels = {
            error: 0,
            warn: 1,
            info: 2,
            debug: 3
        };
    }

    /**
     * Get current timestamp
     */
    getTimestamp() {
        return new Date().toISOString();
    }

    /**
     * Format log message
     */
    formatMessage(level, message, meta = {}) {
        const timestamp = this.getTimestamp();
        const metaStr = Object.keys(meta).length > 0 ? ` ${JSON.stringify(meta)}` : '';
        return `[${timestamp}] ${level.toUpperCase()}: ${message}${metaStr}`;
    }

    /**
     * Check if log level should be logged
     */
    shouldLog(level) {
        return this.levels[level] <= this.levels[this.logLevel];
    }

    /**
     * Write log to file
     */
    async writeToFile(formattedMessage) {
        if (!this.logFile) return;

        try {
            // Ensure log directory exists
            const dir = path.dirname(this.logFile);
            await fs.mkdir(dir, { recursive: true });
            
            await fs.appendFile(this.logFile, formattedMessage + '\n', 'utf8');
        } catch (error) {
            console.error('Failed to write to log file:', error.message);
        }
    }

    /**
     * Log a message
     */
    async log(level, message, meta = {}) {
        if (!this.shouldLog(level)) return;

        const formattedMessage = this.formatMessage(level, message, meta);

        // Console output
        if (this.enableConsole) {
            switch (level) {
                case 'error':
                    console.error(formattedMessage);
                    break;
                case 'warn':
                    console.warn(formattedMessage);
                    break;
                case 'debug':
                    console.debug(formattedMessage);
                    break;
                default:
                    console.log(formattedMessage);
            }
        }

        // File output
        await this.writeToFile(formattedMessage);
    }

    /**
     * Log error message
     */
    async error(message, meta = {}) {
        await this.log('error', message, meta);
    }

    /**
     * Log warning message
     */
    async warn(message, meta = {}) {
        await this.log('warn', message, meta);
    }

    /**
     * Log info message
     */
    async info(message, meta = {}) {
        await this.log('info', message, meta);
    }

    /**
     * Log debug message
     */
    async debug(message, meta = {}) {
        await this.log('debug', message, meta);
    }

    /**
     * Log error with stack trace
     */
    async logError(error, context = '') {
        const message = context ? `${context}: ${error.message}` : error.message;
        const meta = {
            stack: error.stack,
            name: error.name
        };
        await this.error(message, meta);
    }
}

// Create default logger instance
const defaultLogger = new Logger({
    logFile: './logs/follower-tracker.log',
    logLevel: 'info'
});

module.exports = {
    Logger,
    logger: defaultLogger
};
