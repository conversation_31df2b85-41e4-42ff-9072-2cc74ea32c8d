#!/usr/bin/env node

/**
 * Basic test script for Instagram Follower Tracker
 * This is a simple test to verify core functionality works
 */

const path = require('path');
const fs = require('fs').promises;
const { FileIO } = require('../src/utils');
const { User, FriendshipStatus } = require('../src/models');
const { FollowerComparisonService } = require('../src/services');

async function runBasicTests() {
    console.log('🧪 Running basic tests for Instagram Follower Tracker\n');

    try {
        // Test 1: File I/O
        console.log('📖 Test 1: File I/O Operations');
        const followersPath = path.join(__dirname, 'sample_data/followers.json');
        const statusPath = path.join(__dirname, 'sample_data/friendship_status.json');

        const followers = await FileIO.readFollowersData(followersPath);
        console.log(`✅ Successfully read ${followers.length} followers`);

        const statuses = await FileIO.readFriendshipStatusData(statusPath);
        console.log(`✅ Successfully read ${statuses.length} friendship statuses`);

        // Test 2: Data Models
        console.log('\n🏗️  Test 2: Data Models');
        const firstFollower = followers[0];
        console.log(`✅ User model: ${firstFollower.username} (${firstFollower.full_name})`);

        const firstStatus = statuses[0];
        console.log(`✅ FriendshipStatus model: ${firstStatus.pk} - Following: ${firstStatus.following}`);

        // Test 3: Comparison Logic
        console.log('\n🔄 Test 3: Comparison Logic');
        
        // Simulate previous data (empty for first run)
        const previousFollowers = [];
        const previousStatuses = [];

        const comparison = FollowerComparisonService.compareFollowers(
            followers,
            previousFollowers,
            statuses,
            previousStatuses
        );

        console.log(`✅ Comparison completed:`);
        console.log(`   - New followers: ${comparison.summary.newFollowersCount}`);
        console.log(`   - Unfollowed: ${comparison.summary.unfollowedCount}`);
        console.log(`   - Status changes: ${comparison.summary.statusChangesCount}`);
        console.log(`   - Total changes: ${comparison.changes.length}`);

        // Test 4: Summary Report
        console.log('\n📊 Test 4: Summary Report Generation');
        const report = FollowerComparisonService.generateSummaryReport(comparison);
        console.log('✅ Summary report generated successfully');
        console.log(report);

        // Test 5: Data Conversion
        console.log('🔄 Test 5: Data Conversion');
        const csvRow = firstFollower.toCSVRow();
        console.log(`✅ CSV conversion: ${Object.keys(csvRow).length} fields`);

        const dbRow = firstFollower.toDBRow();
        console.log(`✅ DB conversion: ${Object.keys(dbRow).length} fields`);

        console.log('\n🎉 All basic tests passed successfully!');
        console.log('\n📝 Next steps:');
        console.log('   1. Try running: npm start track -f tests/sample_data/followers.json -s tests/sample_data/friendship_status.json');
        console.log('   2. Check the generated reports in ./reports/');
        console.log('   3. View the database at ./data/followers.db');

    } catch (error) {
        console.error('❌ Test failed:', error.message);
        console.error(error.stack);
        process.exit(1);
    }
}

// Run tests if this file is executed directly
if (require.main === module) {
    runBasicTests();
}

module.exports = { runBasicTests };
