const sqlite3 = require('sqlite3').verbose();
const path = require('path');
const fs = require('fs').promises;
const { User, FriendshipStatus, FollowerChange } = require('../models');

/**
 * Database service for SQLite operations
 */
class DatabaseService {
    constructor(dbPath = './data/followers.db') {
        this.dbPath = dbPath;
        this.db = null;
    }

    /**
     * Initialize database connection and create tables
     */
    async initialize() {
        // Ensure data directory exists
        const dir = path.dirname(this.dbPath);
        await fs.mkdir(dir, { recursive: true });

        return new Promise((resolve, reject) => {
            this.db = new sqlite3.Database(this.dbPath, (err) => {
                if (err) {
                    reject(new Error(`Failed to connect to database: ${err.message}`));
                } else {
                    console.log(`Connected to SQLite database: ${this.dbPath}`);
                    this.createTables()
                        .then(() => resolve())
                        .catch(reject);
                }
            });
        });
    }

    /**
     * Create database tables
     */
    async createTables() {
        const createUsersTable = `
            CREATE TABLE IF NOT EXISTS users (
                pk TEXT PRIMARY KEY,
                pk_id TEXT,
                id TEXT,
                full_name TEXT,
                username TEXT,
                is_private INTEGER,
                is_verified INTEGER,
                profile_pic_url TEXT,
                profile_pic_id TEXT,
                fbid_v2 TEXT,
                strong_id__ TEXT,
                third_party_downloads_enabled INTEGER,
                has_anonymous_profile_picture INTEGER,
                account_badges TEXT,
                latest_reel_media INTEGER,
                is_favorite INTEGER,
                last_seen DATETIME DEFAULT CURRENT_TIMESTAMP,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        `;

        const createFriendshipStatusTable = `
            CREATE TABLE IF NOT EXISTS friendship_statuses (
                pk TEXT PRIMARY KEY,
                following INTEGER,
                incoming_request INTEGER,
                is_bestie INTEGER,
                is_private INTEGER,
                is_restricted INTEGER,
                outgoing_request INTEGER,
                is_feed_favorite INTEGER,
                last_updated DATETIME DEFAULT CURRENT_TIMESTAMP,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        `;

        const createFollowerChangesTable = `
            CREATE TABLE IF NOT EXISTS follower_changes (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                pk TEXT,
                username TEXT,
                full_name TEXT,
                change_type TEXT,
                timestamp DATETIME,
                previous_status TEXT,
                current_status TEXT,
                metadata TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        `;

        const createIndexes = [
            'CREATE INDEX IF NOT EXISTS idx_users_username ON users(username)',
            'CREATE INDEX IF NOT EXISTS idx_users_last_seen ON users(last_seen)',
            'CREATE INDEX IF NOT EXISTS idx_friendship_statuses_last_updated ON friendship_statuses(last_updated)',
            'CREATE INDEX IF NOT EXISTS idx_follower_changes_pk ON follower_changes(pk)',
            'CREATE INDEX IF NOT EXISTS idx_follower_changes_timestamp ON follower_changes(timestamp)',
            'CREATE INDEX IF NOT EXISTS idx_follower_changes_change_type ON follower_changes(change_type)'
        ];

        try {
            await this.runQuery(createUsersTable);
            await this.runQuery(createFriendshipStatusTable);
            await this.runQuery(createFollowerChangesTable);

            for (const indexQuery of createIndexes) {
                await this.runQuery(indexQuery);
            }

            console.log('Database tables created successfully');
        } catch (error) {
            throw new Error(`Failed to create tables: ${error.message}`);
        }
    }

    /**
     * Run a SQL query
     * @param {string} sql - SQL query
     * @param {Array} params - Query parameters
     * @returns {Promise} Promise that resolves with query result
     */
    runQuery(sql, params = []) {
        return new Promise((resolve, reject) => {
            this.db.run(sql, params, function(err) {
                if (err) {
                    reject(err);
                } else {
                    resolve({ lastID: this.lastID, changes: this.changes });
                }
            });
        });
    }

    /**
     * Get all rows from a query
     * @param {string} sql - SQL query
     * @param {Array} params - Query parameters
     * @returns {Promise<Array>} Promise that resolves with array of rows
     */
    getAllRows(sql, params = []) {
        return new Promise((resolve, reject) => {
            this.db.all(sql, params, (err, rows) => {
                if (err) {
                    reject(err);
                } else {
                    resolve(rows);
                }
            });
        });
    }

    /**
     * Get a single row from a query
     * @param {string} sql - SQL query
     * @param {Array} params - Query parameters
     * @returns {Promise<Object>} Promise that resolves with a single row
     */
    getRow(sql, params = []) {
        return new Promise((resolve, reject) => {
            this.db.get(sql, params, (err, row) => {
                if (err) {
                    reject(err);
                } else {
                    resolve(row);
                }
            });
        });
    }

    /**
     * Save or update users in the database
     * @param {User[]} users - Array of User objects
     */
    async saveUsers(users) {
        const sql = `
            INSERT OR REPLACE INTO users (
                pk, pk_id, id, full_name, username, is_private, is_verified,
                profile_pic_url, profile_pic_id, fbid_v2, strong_id__,
                third_party_downloads_enabled, has_anonymous_profile_picture,
                account_badges, latest_reel_media, is_favorite, last_seen
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        `;

        for (const user of users) {
            const row = user.toDBRow();
            await this.runQuery(sql, [
                row.pk, row.pk_id, row.id, row.full_name, row.username,
                row.is_private, row.is_verified, row.profile_pic_url,
                row.profile_pic_id, row.fbid_v2, row.strong_id__,
                row.third_party_downloads_enabled, row.has_anonymous_profile_picture,
                row.account_badges, row.latest_reel_media, row.is_favorite
            ]);
        }
    }

    /**
     * Get all users from the database
     * @returns {Promise<User[]>} Array of User objects
     */
    async getAllUsers() {
        const sql = 'SELECT * FROM users ORDER BY username';
        const rows = await this.getAllRows(sql);
        return rows.map(row => User.fromDBRow(row));
    }

    /**
     * Save or update friendship statuses in the database
     * @param {FriendshipStatus[]} statuses - Array of FriendshipStatus objects
     */
    async saveFriendshipStatuses(statuses) {
        const sql = `
            INSERT OR REPLACE INTO friendship_statuses (
                pk, following, incoming_request, is_bestie, is_private,
                is_restricted, outgoing_request, is_feed_favorite, last_updated
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        `;

        for (const status of statuses) {
            const row = status.toDBRow();
            await this.runQuery(sql, [
                row.pk, row.following, row.incoming_request, row.is_bestie,
                row.is_private, row.is_restricted, row.outgoing_request, row.is_feed_favorite
            ]);
        }
    }

    /**
     * Get all friendship statuses from the database
     * @returns {Promise<FriendshipStatus[]>} Array of FriendshipStatus objects
     */
    async getAllFriendshipStatuses() {
        const sql = 'SELECT * FROM friendship_statuses';
        const rows = await this.getAllRows(sql);
        return rows.map(row => FriendshipStatus.fromDBRow(row));
    }

    /**
     * Save follower changes to the database
     * @param {FollowerChange[]} changes - Array of FollowerChange objects
     */
    async saveFollowerChanges(changes) {
        const sql = `
            INSERT INTO follower_changes (
                pk, username, full_name, change_type, timestamp,
                previous_status, current_status, metadata
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        `;

        for (const change of changes) {
            const row = change.toDBRow();
            await this.runQuery(sql, [
                row.pk, row.username, row.full_name, row.change_type,
                row.timestamp, row.previous_status, row.current_status, row.metadata
            ]);
        }
    }

    /**
     * Get follower changes from the database
     * @param {Object} options - Query options
     * @returns {Promise<FollowerChange[]>} Array of FollowerChange objects
     */
    async getFollowerChanges(options = {}) {
        let sql = 'SELECT * FROM follower_changes';
        const params = [];
        const conditions = [];

        if (options.changeType) {
            conditions.push('change_type = ?');
            params.push(options.changeType);
        }

        if (options.since) {
            conditions.push('timestamp >= ?');
            params.push(options.since);
        }

        if (options.until) {
            conditions.push('timestamp <= ?');
            params.push(options.until);
        }

        if (conditions.length > 0) {
            sql += ' WHERE ' + conditions.join(' AND ');
        }

        sql += ' ORDER BY timestamp DESC';

        if (options.limit) {
            sql += ' LIMIT ?';
            params.push(options.limit);
        }

        const rows = await this.getAllRows(sql, params);
        return rows.map(row => FollowerChange.fromDBRow(row));
    }

    /**
     * Close database connection
     */
    async close() {
        if (this.db) {
            return new Promise((resolve, reject) => {
                this.db.close((err) => {
                    if (err) {
                        reject(err);
                    } else {
                        console.log('Database connection closed');
                        resolve();
                    }
                });
            });
        }
    }
}

module.exports = DatabaseService;
