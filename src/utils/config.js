const fs = require('fs').promises;
const path = require('path');

/**
 * Configuration management utility
 */
class Config {
    constructor() {
        this.config = {};
        this.loaded = false;
    }

    /**
     * Load configuration from file
     * @param {string} configPath - Path to config file
     */
    async load(configPath = null) {
        const defaultConfigPath = path.join(__dirname, '../../config/default.json');
        const userConfigPath = configPath || path.join(process.cwd(), 'follower-tracker.config.json');

        try {
            // Load default configuration
            const defaultConfig = await this.loadConfigFile(defaultConfigPath);
            this.config = { ...defaultConfig };

            // Try to load user configuration and merge
            try {
                const userConfig = await this.loadConfigFile(userConfigPath);
                this.config = this.mergeConfig(this.config, userConfig);
                console.log(`📋 Loaded user configuration from: ${userConfigPath}`);
            } catch (error) {
                if (error.code !== 'ENOENT') {
                    console.warn(`⚠️  Warning: Could not load user config: ${error.message}`);
                }
                // Use default config if user config doesn't exist or is invalid
                console.log('📋 Using default configuration');
            }

            this.loaded = true;
        } catch (error) {
            throw new Error(`Failed to load configuration: ${error.message}`);
        }
    }

    /**
     * Load configuration from a JSON file
     * @param {string} filePath - Path to the config file
     * @returns {Object} Parsed configuration
     */
    async loadConfigFile(filePath) {
        try {
            const data = await fs.readFile(filePath, 'utf8');
            return JSON.parse(data);
        } catch (error) {
            if (error.code === 'ENOENT') {
                throw new Error(`Configuration file not found: ${filePath}`);
            }
            if (error instanceof SyntaxError) {
                throw new Error(`Invalid JSON in configuration file: ${filePath}`);
            }
            throw error;
        }
    }

    /**
     * Deep merge two configuration objects
     * @param {Object} target - Target configuration
     * @param {Object} source - Source configuration to merge
     * @returns {Object} Merged configuration
     */
    mergeConfig(target, source) {
        const result = { ...target };

        for (const key in source) {
            if (source.hasOwnProperty(key)) {
                if (typeof source[key] === 'object' && source[key] !== null && !Array.isArray(source[key])) {
                    result[key] = this.mergeConfig(target[key] || {}, source[key]);
                } else {
                    result[key] = source[key];
                }
            }
        }

        return result;
    }

    /**
     * Get configuration value by path
     * @param {string} path - Dot-separated path to the config value
     * @param {*} defaultValue - Default value if path doesn't exist
     * @returns {*} Configuration value
     */
    get(path, defaultValue = undefined) {
        if (!this.loaded) {
            throw new Error('Configuration not loaded. Call load() first.');
        }

        const keys = path.split('.');
        let current = this.config;

        for (const key of keys) {
            if (current && typeof current === 'object' && key in current) {
                current = current[key];
            } else {
                return defaultValue;
            }
        }

        return current;
    }

    /**
     * Set configuration value by path
     * @param {string} path - Dot-separated path to the config value
     * @param {*} value - Value to set
     */
    set(path, value) {
        if (!this.loaded) {
            throw new Error('Configuration not loaded. Call load() first.');
        }

        const keys = path.split('.');
        let current = this.config;

        for (let i = 0; i < keys.length - 1; i++) {
            const key = keys[i];
            if (!(key in current) || typeof current[key] !== 'object') {
                current[key] = {};
            }
            current = current[key];
        }

        current[keys[keys.length - 1]] = value;
    }

    /**
     * Get all configuration
     * @returns {Object} Complete configuration object
     */
    getAll() {
        if (!this.loaded) {
            throw new Error('Configuration not loaded. Call load() first.');
        }
        return { ...this.config };
    }

    /**
     * Save current configuration to file
     * @param {string} filePath - Path to save the config file
     */
    async save(filePath) {
        if (!this.loaded) {
            throw new Error('Configuration not loaded. Call load() first.');
        }

        try {
            // Ensure directory exists
            const dir = path.dirname(filePath);
            await fs.mkdir(dir, { recursive: true });

            await fs.writeFile(filePath, JSON.stringify(this.config, null, 2), 'utf8');
            console.log(`💾 Configuration saved to: ${filePath}`);
        } catch (error) {
            throw new Error(`Failed to save configuration: ${error.message}`);
        }
    }

    /**
     * Create a sample configuration file
     * @param {string} filePath - Path to create the sample config
     */
    async createSampleConfig(filePath) {
        const sampleConfig = {
            database: {
                path: "./data/followers.db",
                backupOnUpdate: true
            },
            output: {
                directory: "./reports",
                generateCSV: true,
                timestampFiles: true
            },
            logging: {
                level: "info",
                file: "./logs/follower-tracker.log",
                enableConsole: true
            },
            tracking: {
                autoBackup: true,
                validateInput: true,
                trackStatusChanges: true
            }
        };

        try {
            // Ensure directory exists
            const dir = path.dirname(filePath);
            await fs.mkdir(dir, { recursive: true });

            await fs.writeFile(filePath, JSON.stringify(sampleConfig, null, 2), 'utf8');
            console.log(`📝 Sample configuration created at: ${filePath}`);
        } catch (error) {
            throw new Error(`Failed to create sample configuration: ${error.message}`);
        }
    }
}

// Create and export a singleton instance
const config = new Config();

module.exports = {
    Config,
    config
};
