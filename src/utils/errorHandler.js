const { logger } = require('./logger');

/**
 * Custom error classes
 */
class FollowerTrackerError extends Error {
    constructor(message, code = 'GENERAL_ERROR', details = {}) {
        super(message);
        this.name = 'FollowerTrackerError';
        this.code = code;
        this.details = details;
    }
}

class FileError extends FollowerTrackerError {
    constructor(message, filePath, details = {}) {
        super(message, 'FILE_ERROR', { filePath, ...details });
        this.name = 'FileError';
    }
}

class DatabaseError extends FollowerTrackerError {
    constructor(message, query = null, details = {}) {
        super(message, 'DATABASE_ERROR', { query, ...details });
        this.name = 'DatabaseError';
    }
}

class ValidationError extends FollowerTrackerError {
    constructor(message, field = null, value = null, details = {}) {
        super(message, 'VALIDATION_ERROR', { field, value, ...details });
        this.name = 'ValidationError';
    }
}

/**
 * Error handling utilities
 */
class ErrorHandler {
    /**
     * Handle and log errors appropriately
     * @param {Error} error - The error to handle
     * @param {string} context - Context where the error occurred
     * @param {boolean} shouldThrow - Whether to re-throw the error
     */
    static async handleError(error, context = '', shouldThrow = true) {
        // Log the error
        await logger.logError(error, context);

        // Handle specific error types
        if (error instanceof FollowerTrackerError) {
            console.error(`❌ ${error.name}: ${error.message}`);
            if (error.details && Object.keys(error.details).length > 0) {
                console.error('Details:', error.details);
            }
        } else if (error.code === 'ENOENT') {
            console.error(`❌ File not found: ${error.path}`);
        } else if (error.code === 'EACCES') {
            console.error(`❌ Permission denied: ${error.path}`);
        } else if (error instanceof SyntaxError && error.message.includes('JSON')) {
            console.error(`❌ Invalid JSON format in file`);
        } else {
            console.error(`❌ Unexpected error: ${error.message}`);
        }

        if (shouldThrow) {
            throw error;
        }
    }

    /**
     * Wrap async functions with error handling
     * @param {Function} fn - Async function to wrap
     * @param {string} context - Context for error logging
     * @returns {Function} Wrapped function
     */
    static wrapAsync(fn, context = '') {
        return async (...args) => {
            try {
                return await fn(...args);
            } catch (error) {
                await this.handleError(error, context);
            }
        };
    }

    /**
     * Validate required parameters
     * @param {Object} params - Parameters to validate
     * @param {string[]} required - Required parameter names
     * @throws {ValidationError} If validation fails
     */
    static validateRequired(params, required) {
        for (const param of required) {
            if (params[param] === undefined || params[param] === null) {
                throw new ValidationError(`Missing required parameter: ${param}`, param);
            }
        }
    }

    /**
     * Validate file exists and is readable
     * @param {string} filePath - Path to validate
     * @throws {FileError} If file is not accessible
     */
    static async validateFile(filePath) {
        const fs = require('fs').promises;
        try {
            await fs.access(filePath, fs.constants.R_OK);
        } catch (error) {
            if (error.code === 'ENOENT') {
                throw new FileError(`File not found: ${filePath}`, filePath);
            } else if (error.code === 'EACCES') {
                throw new FileError(`Permission denied: ${filePath}`, filePath);
            } else {
                throw new FileError(`Cannot access file: ${filePath}`, filePath, { originalError: error.message });
            }
        }
    }

    /**
     * Validate JSON structure
     * @param {Object} data - Data to validate
     * @param {Object} schema - Expected schema
     * @throws {ValidationError} If validation fails
     */
    static validateJSONStructure(data, schema) {
        for (const [key, type] of Object.entries(schema)) {
            if (!(key in data)) {
                throw new ValidationError(`Missing required field: ${key}`, key);
            }
            
            if (type === 'array' && !Array.isArray(data[key])) {
                throw new ValidationError(`Field ${key} must be an array`, key, data[key]);
            }
            
            if (type === 'object' && (typeof data[key] !== 'object' || Array.isArray(data[key]))) {
                throw new ValidationError(`Field ${key} must be an object`, key, data[key]);
            }
        }
    }

    /**
     * Create a safe async wrapper that doesn't throw
     * @param {Function} fn - Async function to wrap
     * @param {*} defaultValue - Default value to return on error
     * @returns {Function} Safe wrapped function
     */
    static safe(fn, defaultValue = null) {
        return async (...args) => {
            try {
                return await fn(...args);
            } catch (error) {
                await logger.logError(error, 'Safe wrapper caught error');
                return defaultValue;
            }
        };
    }
}

// Global error handlers
process.on('uncaughtException', async (error) => {
    await logger.logError(error, 'Uncaught Exception');
    console.error('❌ Fatal error occurred. Check logs for details.');
    process.exit(1);
});

process.on('unhandledRejection', async (reason, promise) => {
    const error = reason instanceof Error ? reason : new Error(String(reason));
    await logger.logError(error, 'Unhandled Promise Rejection');
    console.error('❌ Unhandled promise rejection. Check logs for details.');
});

module.exports = {
    ErrorHandler,
    FollowerTrackerError,
    FileError,
    DatabaseError,
    ValidationError
};
