#!/usr/bin/env node

const { Command } = require('commander');
const path = require('path');
const { FileIO, CSVWriter } = require('./utils');
const { DatabaseService, FollowerComparisonService } = require('./services');

/**
 * Main application class for Instagram follower tracking
 */
class FollowerTracker {
    constructor() {
        this.db = new DatabaseService();
        this.program = new Command();
        this.setupCLI();
    }

    /**
     * Setup command line interface
     */
    setupCLI() {
        this.program
            .name('track-followers')
            .description('Instagram follower tracking CLI tool')
            .version('1.0.0');

        this.program
            .command('track')
            .description('Track followers from JSON files')
            .requiredOption('-f, --followers <path>', 'Path to followers JSON file')
            .option('-s, --status <path>', 'Path to friendship status JSON file')
            .option('-o, --output <path>', 'Output directory for CSV reports (default: ./reports)')
            .option('-d, --database <path>', 'Database file path (default: ./data/followers.db)')
            .option('--no-csv', 'Skip CSV report generation')
            .action(async (options) => {
                try {
                    await this.trackFollowers(options);
                } catch (error) {
                    console.error('Error:', error.message);
                    process.exit(1);
                }
            });

        this.program
            .command('report')
            .description('Generate reports from stored data')
            .option('-o, --output <path>', 'Output directory for reports (default: ./reports)')
            .option('-d, --database <path>', 'Database file path (default: ./data/followers.db)')
            .option('--since <date>', 'Show changes since date (YYYY-MM-DD)')
            .option('--type <type>', 'Filter by change type (NEW_FOLLOWER, UNFOLLOWED, STATUS_CHANGE)')
            .option('--limit <number>', 'Limit number of results')
            .action(async (options) => {
                try {
                    await this.generateReport(options);
                } catch (error) {
                    console.error('Error:', error.message);
                    process.exit(1);
                }
            });
    }

    /**
     * Main tracking function
     * @param {Object} options - Command line options
     */
    async trackFollowers(options) {
        console.log('🔍 Starting follower tracking...\n');

        // Initialize database
        if (options.database) {
            this.db = new DatabaseService(options.database);
        }
        await this.db.initialize();

        try {
            // Read current data from JSON files
            console.log('📖 Reading current followers data...');
            const currentFollowers = await FileIO.readFollowersData(options.followers);
            console.log(`Found ${currentFollowers.length} current followers`);

            let currentStatuses = [];
            if (options.status) {
                console.log('📖 Reading friendship status data...');
                currentStatuses = await FileIO.readFriendshipStatusData(options.status);
                console.log(`Found ${currentStatuses.length} friendship statuses`);
            }

            // Get previous data from database
            console.log('🗄️  Loading previous data from database...');
            const previousFollowers = await this.db.getAllUsers();
            const previousStatuses = await this.db.getAllFriendshipStatuses();
            console.log(`Found ${previousFollowers.length} previous followers in database`);

            // Compare data and detect changes
            console.log('🔄 Comparing followers and detecting changes...');
            const comparison = FollowerComparisonService.compareFollowers(
                currentFollowers,
                previousFollowers,
                currentStatuses,
                previousStatuses
            );

            // Display summary
            const summaryReport = FollowerComparisonService.generateSummaryReport(comparison);
            console.log(summaryReport);

            // Save current data to database
            console.log('💾 Saving current data to database...');
            await this.db.saveUsers(currentFollowers);
            if (currentStatuses.length > 0) {
                await this.db.saveFriendshipStatuses(currentStatuses);
            }

            // Save changes to database
            if (comparison.changes.length > 0) {
                await this.db.saveFollowerChanges(comparison.changes);
                console.log(`Saved ${comparison.changes.length} changes to database`);
            }

            // Generate CSV reports if requested
            if (options.csv !== false) {
                const outputDir = options.output || './reports';
                await this.generateCSVReports(comparison, currentFollowers, currentStatuses, outputDir);
            }

            console.log('\n✅ Tracking completed successfully!');

        } finally {
            await this.db.close();
        }
    }

    /**
     * Generate CSV reports
     * @param {Object} comparison - Comparison results
     * @param {User[]} currentFollowers - Current followers
     * @param {FriendshipStatus[]} currentStatuses - Current statuses
     * @param {string} outputDir - Output directory
     */
    async generateCSVReports(comparison, currentFollowers, currentStatuses, outputDir) {
        console.log('\n📊 Generating CSV reports...');

        const date = new Date().toISOString().split('T')[0]; // YYYY-MM-DD format

        // Generate main report with all current followers and changes
        const mainFile = path.join(outputDir, `followers_report_${date}.csv`);
        await CSVWriter.writeFollowersReport(currentFollowers, currentStatuses, comparison.changes, mainFile);
        console.log(`\n🎯 Main report: ${mainFile}`);
        console.log(`   - ${currentFollowers.length} current followers`);
        if (comparison.changes.length > 0) {
            console.log(`   - ${comparison.summary.newFollowersCount} new followers`);
            console.log(`   - ${comparison.summary.unfollowedCount} unfollowed`);
            console.log(`   - ${comparison.summary.statusChangesCount} status changes`);
        } else {
            console.log(`   - No changes since last run`);
        }

        // Optionally generate current followers snapshot (less important)
        const followersFile = path.join(outputDir, `current_followers_${date}.csv`);
        await CSVWriter.writeCurrentFollowers(currentFollowers, followersFile);

        console.log(`\nAll reports saved to: ${outputDir}`);
    }

    /**
     * Generate reports from stored data
     * @param {Object} options - Command line options
     */
    async generateReport(options) {
        console.log('📊 Generating reports from stored data...\n');

        // Initialize database
        if (options.database) {
            this.db = new DatabaseService(options.database);
        }
        await this.db.initialize();

        try {
            const queryOptions = {};

            if (options.since) {
                queryOptions.since = options.since;
            }

            if (options.type) {
                queryOptions.changeType = options.type;
            }

            if (options.limit) {
                queryOptions.limit = parseInt(options.limit);
            }

            const changes = await this.db.getFollowerChanges(queryOptions);

            if (changes.length === 0) {
                console.log('No changes found matching the criteria.');
                return;
            }

            console.log(`Found ${changes.length} changes:`);

            // Group changes by type
            const grouped = changes.reduce((acc, change) => {
                if (!acc[change.change_type]) {
                    acc[change.change_type] = [];
                }
                acc[change.change_type].push(change);
                return acc;
            }, {});

            for (const [type, typeChanges] of Object.entries(grouped)) {
                console.log(`\n${type} (${typeChanges.length}):`);
                typeChanges.forEach(change => {
                    console.log(`  ${change.timestamp} - ${change.username} (${change.full_name})`);
                });
            }

            // Generate CSV report
            const outputDir = options.output || './reports';
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
            const reportFile = path.join(outputDir, `report_${timestamp}.csv`);
            await CSVWriter.writeFollowerChanges(changes, reportFile);

        } finally {
            await this.db.close();
        }
    }

    /**
     * Run the CLI application
     */
    async run() {
        await this.program.parseAsync(process.argv);
    }
}

// Run the application if this file is executed directly
if (require.main === module) {
    const tracker = new FollowerTracker();
    tracker.run().catch(error => {
        console.error('Fatal error:', error.message);
        process.exit(1);
    });
}

module.exports = FollowerTracker;
