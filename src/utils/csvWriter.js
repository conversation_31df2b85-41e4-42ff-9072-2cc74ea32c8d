const createCsvWriter = require('csv-writer').createObjectCsvWriter;
const path = require('path');
const fs = require('fs').promises;

/**
 * CSV writing utilities for exporting tracking data
 */
class CSVWriter {
    /**
     * Write follower changes to CSV
     * @param {FollowerChange[]} changes - Array of follower changes
     * @param {string} outputPath - Path to write the CSV file
     */
    static async writeFollowerChanges(changes, outputPath) {
        if (!changes || changes.length === 0) {
            console.log('No changes to write to CSV');
            return;
        }

        // Ensure directory exists
        const dir = path.dirname(outputPath);
        await fs.mkdir(dir, { recursive: true });

        const csvWriter = createCsvWriter({
            path: outputPath,
            header: [
                { id: 'id', title: 'ID' },
                { id: 'pk', title: 'User PK' },
                { id: 'username', title: 'Username' },
                { id: 'full_name', title: 'Full Name' },
                { id: 'change_type', title: 'Change Type' },
                { id: 'timestamp', title: 'Timestamp' },
                { id: 'previous_status', title: 'Previous Status' },
                { id: 'current_status', title: 'Current Status' },
                { id: 'metadata', title: 'Metadata' }
            ]
        });

        const records = changes.map(change => change.toCSVRow());
        
        try {
            await csvWriter.writeRecords(records);
            console.log(`Successfully wrote ${records.length} follower changes to ${outputPath}`);
        } catch (error) {
            throw new Error(`Failed to write CSV file: ${error.message}`);
        }
    }

    /**
     * Write current followers to CSV
     * @param {User[]} users - Array of users
     * @param {string} outputPath - Path to write the CSV file
     */
    static async writeCurrentFollowers(users, outputPath) {
        if (!users || users.length === 0) {
            console.log('No followers to write to CSV');
            return;
        }

        // Ensure directory exists
        const dir = path.dirname(outputPath);
        await fs.mkdir(dir, { recursive: true });

        const csvWriter = createCsvWriter({
            path: outputPath,
            header: [
                { id: 'pk', title: 'User PK' },
                { id: 'username', title: 'Username' },
                { id: 'full_name', title: 'Full Name' },
                { id: 'is_private', title: 'Is Private' },
                { id: 'is_verified', title: 'Is Verified' },
                { id: 'profile_pic_url', title: 'Profile Picture URL' }
            ]
        });

        const records = users.map(user => user.toCSVRow());
        
        try {
            await csvWriter.writeRecords(records);
            console.log(`Successfully wrote ${records.length} current followers to ${outputPath}`);
        } catch (error) {
            throw new Error(`Failed to write CSV file: ${error.message}`);
        }
    }

    /**
     * Write friendship statuses to CSV
     * @param {FriendshipStatus[]} statuses - Array of friendship statuses
     * @param {string} outputPath - Path to write the CSV file
     */
    static async writeFriendshipStatuses(statuses, outputPath) {
        if (!statuses || statuses.length === 0) {
            console.log('No friendship statuses to write to CSV');
            return;
        }

        // Ensure directory exists
        const dir = path.dirname(outputPath);
        await fs.mkdir(dir, { recursive: true });

        const csvWriter = createCsvWriter({
            path: outputPath,
            header: [
                { id: 'pk', title: 'User PK' },
                { id: 'following', title: 'Following' },
                { id: 'incoming_request', title: 'Incoming Request' },
                { id: 'is_bestie', title: 'Is Bestie' },
                { id: 'is_private', title: 'Is Private' },
                { id: 'is_restricted', title: 'Is Restricted' },
                { id: 'outgoing_request', title: 'Outgoing Request' },
                { id: 'is_feed_favorite', title: 'Is Feed Favorite' }
            ]
        });

        const records = statuses.map(status => status.toCSVRow());
        
        try {
            await csvWriter.writeRecords(records);
            console.log(`Successfully wrote ${records.length} friendship statuses to ${outputPath}`);
        } catch (error) {
            throw new Error(`Failed to write CSV file: ${error.message}`);
        }
    }

    /**
     * Generate timestamped filename
     * @param {string} baseName - Base name for the file
     * @param {string} extension - File extension (default: 'csv')
     * @returns {string} Timestamped filename
     */
    static generateTimestampedFilename(baseName, extension = 'csv') {
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        return `${baseName}_${timestamp}.${extension}`;
    }
}

module.exports = CSVWriter;
