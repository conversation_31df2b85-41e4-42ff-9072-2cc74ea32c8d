/**
 * FriendshipStatus model representing the relationship status with a user
 */
class FriendshipStatus {
    constructor(pk, statusData) {
        this.pk = pk;
        this.following = statusData.following;
        this.incoming_request = statusData.incoming_request;
        this.is_bestie = statusData.is_bestie;
        this.is_private = statusData.is_private;
        this.is_restricted = statusData.is_restricted;
        this.outgoing_request = statusData.outgoing_request;
        this.is_feed_favorite = statusData.is_feed_favorite;
    }

    /**
     * Get the unique identifier for this friendship status
     */
    getId() {
        return this.pk;
    }

    /**
     * Get a simplified representation for CSV export
     */
    toCSVRow() {
        return {
            pk: this.pk,
            following: this.following,
            incoming_request: this.incoming_request,
            is_bestie: this.is_bestie,
            is_private: this.is_private,
            is_restricted: this.is_restricted,
            outgoing_request: this.outgoing_request,
            is_feed_favorite: this.is_feed_favorite
        };
    }

    /**
     * Create FriendshipStatus instance from database row
     */
    static fromDBRow(row) {
        return new FriendshipStatus(row.pk, {
            following: <PERSON><PERSON><PERSON>(row.following),
            incoming_request: <PERSON><PERSON><PERSON>(row.incoming_request),
            is_bestie: <PERSON><PERSON><PERSON>(row.is_bestie),
            is_private: <PERSON><PERSON><PERSON>(row.is_private),
            is_restricted: <PERSON><PERSON><PERSON>(row.is_restricted),
            outgoing_request: <PERSON>olean(row.outgoing_request),
            is_feed_favorite: Boolean(row.is_feed_favorite)
        });
    }

    /**
     * Convert to database row format
     */
    toDBRow() {
        return {
            pk: this.pk,
            following: this.following ? 1 : 0,
            incoming_request: this.incoming_request ? 1 : 0,
            is_bestie: this.is_bestie ? 1 : 0,
            is_private: this.is_private ? 1 : 0,
            is_restricted: this.is_restricted ? 1 : 0,
            outgoing_request: this.outgoing_request ? 1 : 0,
            is_feed_favorite: this.is_feed_favorite ? 1 : 0
        };
    }
}

module.exports = FriendshipStatus;
