{"database": {"path": "./data/followers.db", "backupOnUpdate": true}, "output": {"directory": "./reports", "generateCSV": true, "timestampFiles": true}, "logging": {"level": "info", "file": "./logs/follower-tracker.log", "enableConsole": true, "maxFileSize": "10MB", "maxFiles": 5}, "tracking": {"autoBackup": true, "validateInput": true, "trackStatusChanges": true}, "reports": {"includeMetadata": true, "defaultLimit": 100, "dateFormat": "YYYY-MM-DD HH:mm:ss"}}