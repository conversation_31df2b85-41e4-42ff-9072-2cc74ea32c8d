const { FollowerChange } = require('../models');

/**
 * Service for comparing follower data and detecting changes
 */
class FollowerComparisonService {
    /**
     * Compare current followers with previous followers to detect changes
     * @param {User[]} currentFollowers - Current followers from JSON
     * @param {User[]} previousFollowers - Previous followers from database
     * @param {FriendshipStatus[]} currentStatuses - Current friendship statuses
     * @param {FriendshipStatus[]} previousStatuses - Previous friendship statuses
     * @returns {Object} Object containing new followers, unfollowed users, and status changes
     */
    static compareFollowers(currentFollowers, previousFollowers = [], currentStatuses = [], previousStatuses = []) {
        // Create maps for efficient lookup
        const currentFollowersMap = new Map(currentFollowers.map(user => [user.pk, user]));
        const previousFollowersMap = new Map(previousFollowers.map(user => [user.pk, user]));
        const currentStatusesMap = new Map(currentStatuses.map(status => [status.pk, status]));
        const previousStatusesMap = new Map(previousStatuses.map(status => [status.pk, status]));

        const changes = [];

        // Find new followers (in current but not in previous)
        const newFollowers = [];
        for (const [pk, user] of currentFollowersMap) {
            if (!previousFollowersMap.has(pk)) {
                newFollowers.push(user);
                const currentStatus = currentStatusesMap.get(pk);
                changes.push(FollowerChange.newFollower(user, currentStatus));
            }
        }

        // Find unfollowed users (in previous but not in current)
        const unfollowedUsers = [];
        for (const [pk, user] of previousFollowersMap) {
            if (!currentFollowersMap.has(pk)) {
                unfollowedUsers.push(user);
                const previousStatus = previousStatusesMap.get(pk);
                changes.push(FollowerChange.unfollowed(user, previousStatus));
            }
        }

        // Find status changes (users present in both but with different statuses)
        const statusChanges = [];
        for (const [pk, currentUser] of currentFollowersMap) {
            if (previousFollowersMap.has(pk)) {
                const currentStatus = currentStatusesMap.get(pk);
                const previousStatus = previousStatusesMap.get(pk);

                if (this.hasStatusChanged(currentStatus, previousStatus)) {
                    statusChanges.push({
                        user: currentUser,
                        previousStatus,
                        currentStatus
                    });
                    changes.push(FollowerChange.statusChange(currentUser, previousStatus, currentStatus));
                }
            }
        }

        return {
            newFollowers,
            unfollowedUsers,
            statusChanges,
            changes,
            summary: {
                totalCurrent: currentFollowers.length,
                totalPrevious: previousFollowers.length,
                newFollowersCount: newFollowers.length,
                unfollowedCount: unfollowedUsers.length,
                statusChangesCount: statusChanges.length,
                netChange: newFollowers.length - unfollowedUsers.length
            }
        };
    }

    /**
     * Check if friendship status has changed between two status objects
     * @param {FriendshipStatus} currentStatus - Current status
     * @param {FriendshipStatus} previousStatus - Previous status
     * @returns {boolean} True if status has changed
     */
    static hasStatusChanged(currentStatus, previousStatus) {
        // If one status exists but not the other, consider it a change
        if (!currentStatus && !previousStatus) return false;
        if (!currentStatus || !previousStatus) return true;

        // Compare all relevant fields
        const fieldsToCompare = [
            'following',
            'incoming_request',
            'is_bestie',
            'is_private',
            'is_restricted',
            'outgoing_request',
            'is_feed_favorite'
        ];

        return fieldsToCompare.some(field => 
            currentStatus[field] !== previousStatus[field]
        );
    }

    /**
     * Generate a summary report of changes
     * @param {Object} comparisonResult - Result from compareFollowers
     * @returns {string} Formatted summary report
     */
    static generateSummaryReport(comparisonResult) {
        const { summary, newFollowers, unfollowedUsers, statusChanges } = comparisonResult;
        
        let report = '\n=== FOLLOWER TRACKING SUMMARY ===\n';
        report += `Timestamp: ${new Date().toISOString()}\n\n`;
        
        report += `Total Current Followers: ${summary.totalCurrent}\n`;
        report += `Total Previous Followers: ${summary.totalPrevious}\n`;
        report += `Net Change: ${summary.netChange > 0 ? '+' : ''}${summary.netChange}\n\n`;
        
        if (summary.newFollowersCount > 0) {
            report += `NEW FOLLOWERS (${summary.newFollowersCount}):\n`;
            newFollowers.forEach(user => {
                report += `  + ${user.username} (${user.full_name}) - ${user.is_private ? 'Private' : 'Public'}\n`;
            });
            report += '\n';
        }
        
        if (summary.unfollowedCount > 0) {
            report += `UNFOLLOWED (${summary.unfollowedCount}):\n`;
            unfollowedUsers.forEach(user => {
                report += `  - ${user.username} (${user.full_name}) - ${user.is_private ? 'Private' : 'Public'}\n`;
            });
            report += '\n';
        }
        
        if (summary.statusChangesCount > 0) {
            report += `STATUS CHANGES (${summary.statusChangesCount}):\n`;
            statusChanges.forEach(change => {
                report += `  ~ ${change.user.username} (${change.user.full_name})\n`;
                if (change.previousStatus && change.currentStatus) {
                    report += `    Following: ${change.previousStatus.following} → ${change.currentStatus.following}\n`;
                    report += `    Private: ${change.previousStatus.is_private} → ${change.currentStatus.is_private}\n`;
                }
            });
            report += '\n';
        }
        
        if (summary.newFollowersCount === 0 && summary.unfollowedCount === 0 && summary.statusChangesCount === 0) {
            report += 'No changes detected.\n\n';
        }
        
        report += '=== END SUMMARY ===\n';
        
        return report;
    }
}

module.exports = FollowerComparisonService;
