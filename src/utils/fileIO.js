const fs = require('fs').promises;
const path = require('path');
const { User, FriendshipStatus } = require('../models');

/**
 * File I/O utilities for reading and writing JSON data
 */
class FileIO {
    /**
     * Read and parse a JSON file
     * @param {string} filePath - Path to the JSON file
     * @returns {Object} Parsed JSON data
     */
    static async readJSON(filePath) {
        try {
            const data = await fs.readFile(filePath, 'utf8');
            return JSON.parse(data);
        } catch (error) {
            if (error.code === 'ENOENT') {
                throw new Error(`File not found: ${filePath}`);
            }
            if (error instanceof SyntaxError) {
                throw new Error(`Invalid JSON in file: ${filePath}`);
            }
            throw error;
        }
    }

    /**
     * Write data to a JSON file
     * @param {string} filePath - Path to write the file
     * @param {Object} data - Data to write
     */
    static async writeJSON(filePath, data) {
        try {
            // Ensure directory exists
            const dir = path.dirname(filePath);
            await fs.mkdir(dir, { recursive: true });
            
            await fs.writeFile(filePath, JSON.stringify(data, null, 2), 'utf8');
        } catch (error) {
            throw new Error(`Failed to write file ${filePath}: ${error.message}`);
        }
    }

    /**
     * Read followers data from Instagram JSON export
     * @param {string} filePath - Path to the followers JSON file
     * @returns {User[]} Array of User objects
     */
    static async readFollowersData(filePath) {
        try {
            const data = await this.readJSON(filePath);
            
            if (!data.users || !Array.isArray(data.users)) {
                throw new Error('Invalid followers file format: missing or invalid "users" array');
            }

            return data.users.map(userData => new User(userData));
        } catch (error) {
            throw new Error(`Failed to read followers data: ${error.message}`);
        }
    }

    /**
     * Read friendship status data from Instagram JSON export
     * @param {string} filePath - Path to the friendship status JSON file
     * @returns {FriendshipStatus[]} Array of FriendshipStatus objects
     */
    static async readFriendshipStatusData(filePath) {
        try {
            const data = await this.readJSON(filePath);
            
            if (!data.friendship_statuses || typeof data.friendship_statuses !== 'object') {
                throw new Error('Invalid friendship status file format: missing or invalid "friendship_statuses" object');
            }

            const friendshipStatuses = [];
            for (const [pk, statusData] of Object.entries(data.friendship_statuses)) {
                friendshipStatuses.push(new FriendshipStatus(pk, statusData));
            }

            return friendshipStatuses;
        } catch (error) {
            throw new Error(`Failed to read friendship status data: ${error.message}`);
        }
    }

    /**
     * Check if a file exists
     * @param {string} filePath - Path to check
     * @returns {boolean} True if file exists
     */
    static async fileExists(filePath) {
        try {
            await fs.access(filePath);
            return true;
        } catch {
            return false;
        }
    }

    /**
     * Get file stats
     * @param {string} filePath - Path to the file
     * @returns {Object} File stats
     */
    static async getFileStats(filePath) {
        try {
            return await fs.stat(filePath);
        } catch (error) {
            throw new Error(`Failed to get file stats for ${filePath}: ${error.message}`);
        }
    }

    /**
     * Create a backup of a file
     * @param {string} filePath - Path to the file to backup
     * @param {string} backupSuffix - Suffix to add to backup file (default: timestamp)
     */
    static async createBackup(filePath, backupSuffix = null) {
        if (!await this.fileExists(filePath)) {
            return null;
        }

        const suffix = backupSuffix || new Date().toISOString().replace(/[:.]/g, '-');
        const backupPath = `${filePath}.backup.${suffix}`;
        
        try {
            await fs.copyFile(filePath, backupPath);
            return backupPath;
        } catch (error) {
            throw new Error(`Failed to create backup: ${error.message}`);
        }
    }
}

module.exports = FileIO;
